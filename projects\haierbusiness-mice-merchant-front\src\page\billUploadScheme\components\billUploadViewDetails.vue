<script setup lang="ts">
// 账单上传详情查看组件（只读模式）
import { onMounted, ref, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  resolveParam,
  routerParam,
  getDealTime,
  errorModal,
  delData,
  meetingProcessOrchestration,
} from '@haierbusiness-front/utils';
import advisors from '@haierbusiness-front/components/mice/advisors/index.vue';
import { message, Modal } from 'ant-design-vue';
import { schemeApi, miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { HotelsArr, hotelLevelConstant, hotelLevelAllConstant } from '@haierbusiness-front/common-libs';

// 账单上传相关组件
import billUploadschemeSupplementEntry from '@haierbusiness-front/components/billUploadScheme/schemeComponent/billUploadschemeSupplementEntry.vue';
import billUploadschemeHotelContract from '@haierbusiness-front/components/billUploadScheme/schemeComponent/billUploadschemeHotelContract.vue';
import billUploadschemeInvoice from '@haierbusiness-front/components/billUploadScheme/schemeComponent/billUploadschemeInvoice.vue';
import billUploadschemeWaterBill from '@haierbusiness-front/components/billUploadScheme/schemeComponent/billUploadschemeWaterBill.vue';
import billUploadschemeAccommodationDetail from '@haierbusiness-front/components/billUploadScheme/schemeComponent/billUploadschemeAccommodationDetail.vue';
import billUploadschemeConferencePhotos from '@haierbusiness-front/components/billUploadScheme/schemeComponent/billUploadschemeConferencePhotos.vue';
import billUploadschemeOtherAttachments from '@haierbusiness-front/components/billUploadScheme/schemeComponent/billUploadschemeOtherAttachments.vue';

import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
const { loginUser } = storeToRefs(applicationStore());

const route = useRoute();
const router = useRouter();
const previewSource = ref<string>('');

const miceId = ref<number>('');
const interactEndDate = ref<string>(''); // 方案提报截止时间
const countdownTime = ref<string>(''); // 方案提报截止时间
const timer = ref(null);

const pdMainId = ref<number>(null);
const pdVerId = ref<number>(null);

// 设置为账单查看模式
const schemeType = ref<string>('billUpload');
const merchantType = ref<number>(null); // 服务商类型

// 账单相关数据
const schemeDetail = ref({});
const billHotelList = ref([]);
const attachmentContracts = ref([]);
const invoiceList = ref([]);
const waterBillList = ref([]);
const accommodationDetailList = ref([]);
const conferencePhotoList = ref([]);
const otherAttachmentList = ref([]);

// 获取用户信息
const getUser = async () => {
  const userInfo = loginUser.value;
  merchantType.value = userInfo.merchantType;
};

// 获取流程详情
const getProcessDetails = async (pdMainId: number, pdVerId: number) => {
  if (!pdMainId) return;
  
  try {
    const res = await meetingProcessOrchestration.getProcessDetails(pdMainId, pdVerId);
    if (res) {
      // 处理流程详情数据
      console.log('流程详情:', res);
    }
  } catch (error) {
    console.error('获取流程详情失败:', error);
  }
};

// 获取账单详情数据
const getBillUploadDetails = async () => {
  try {
    // 这里调用获取账单详情的API
    // const res = await schemeApi.getBillUploadDetails(miceId.value);
    // 模拟数据，实际需要根据API返回的数据结构来设置
    
    // 设置各种账单数据
    // schemeDetail.value = res.schemeDetail || {};
    // billHotelList.value = res.billHotelList || [];
    // attachmentContracts.value = res.attachmentContracts || [];
    // invoiceList.value = res.invoiceList || [];
    // waterBillList.value = res.waterBillList || [];
    // accommodationDetailList.value = res.accommodationDetailList || [];
    // conferencePhotoList.value = res.conferencePhotoList || [];
    // otherAttachmentList.value = res.otherAttachmentList || [];
    
  } catch (error) {
    console.error('获取账单详情失败:', error);
    message.error('获取账单详情失败');
  }
};

// 空的事件处理函数（只读模式不需要处理）
const handleSupplementEntryEmit = () => {};
const handleHotelContractEmit = () => {};
const handleInvoiceEmit = () => {};
const handleWaterBillEmit = () => {};
const handleAccommodationDetailEmit = () => {};
const handleConferencePhotosEmit = () => {};
const handleOtherAttachmentsEmit = () => {};
const handleViewRelatedBill = () => {};

onMounted(async () => {
  const record = resolveParam(route.query.record);
  miceId.value = record.miceId;
  interactEndDate.value = record.interactEndDate;
  pdMainId.value = record.pdMainId || null;
  pdVerId.value = record.pdVerId || null;

  // 获取用户信息
  await getUser();
  
  // 获取流程详情
  await getProcessDetails(pdMainId.value, pdVerId.value);
  
  // 获取账单详情
  await getBillUploadDetails();

  // 设置预览源
  previewSource.value = 'demandOne';
});

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});
</script>

<template>
  <!-- 账单上传详情查看 -->
  <div class="wid1280 bill_upload_view_details">
    <advisors
      v-if="previewSource"
      :preview-source="previewSource"
      :isManagePage="true"
      :platformType="'merchant'"
      :merchantType="merchantType"
      :isReadOnlyMode="true"
    >
      <template #header> </template>
      <template #footer>
        <!-- 只读模式不显示操作按钮 -->
      </template>
    </advisors>
    <!-- 账单上传相关功能（只读模式） -->
    <div class="bill-upload-sections" style="margin-top: 20px">
      <!-- 补充条目 -->
      <div
        class="interact_supplement_entry common_content p24 mb16"
        v-if="merchantType === 1 || merchantType === 2"
      >
        <billUploadschemeSupplementEntry
          :miceId="miceId"
          :schemeDetail="schemeDetail"
          @supplementEntryEmit="handleSupplementEntryEmit"
        />
      </div>

      <!-- 一手合同 -->
      <div class="interact_hotel_contract common_content mb16" v-if="merchantType === 1 || merchantType === 2">
        <billUploadschemeHotelContract
          :hotelList="billHotelList"
          :contractData="attachmentContracts"
          @hotelContractEmit="handleHotelContractEmit"
        />
      </div>

      <!-- 发票信息 -->
      <div class="interact_invoice common_content mb16" v-if="merchantType === 1 || merchantType === 2">
        <billUploadschemeInvoice
          :invoiceList="invoiceList"
          @invoiceEmit="handleInvoiceEmit"
          @viewRelatedBill="handleViewRelatedBill"
        />
      </div>

      <!-- 水单信息 -->
      <div class="interact_water_bill common_content mb16" v-if="merchantType === 1 || merchantType === 2">
        <billUploadschemeWaterBill
          :waterBillList="waterBillList"
          @waterBillEmit="handleWaterBillEmit"
          @viewRelatedBill="handleViewRelatedBill"
        />
      </div>

      <!-- 住宿详单 -->
      <div
        class="interact_accommodation_detail common_content p24 mb16"
        v-if="merchantType === 1 || merchantType === 2"
      >
        <billUploadschemeAccommodationDetail
          :accommodationDetailList="accommodationDetailList"
          @accommodationDetailEmit="handleAccommodationDetailEmit"
        />
      </div>

      <!-- 会议现场照片 -->
      <div
        class="interact_conference_photos common_content p24 mb16"
        v-if="merchantType === 1 || merchantType === 2"
      >
        <billUploadschemeConferencePhotos
          :conferencePhotoList="conferencePhotoList"
          @conferencePhotosEmit="handleConferencePhotosEmit"
        />
      </div>

      <!-- 其他附件 -->
      <div
        class="interact_other_attachments common_content p24 mb16"
        v-if="merchantType === 1 || merchantType === 2"
      >
        <billUploadschemeOtherAttachments
          :otherAttachmentList="otherAttachmentList"
          @otherAttachmentsEmit="handleOtherAttachmentsEmit"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.bill_upload_view_details {
  // 只读模式样式 - 禁用所有输入框
  :deep(.ant-input) {
    background-color: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
  }

  :deep(.ant-input-number) {
    background-color: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;

    .ant-input-number-input {
      background-color: #f5f5f5 !important;
      cursor: not-allowed !important;
    }
  }

  :deep(.ant-select) {
    pointer-events: none !important;

    .ant-select-selector {
      background-color: #f5f5f5 !important;
      border-color: #d9d9d9 !important;
      cursor: not-allowed !important;
    }
  }

  :deep(.ant-date-picker) {
    background-color: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
  }

  :deep(.ant-textarea) {
    background-color: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
  }

  // 隐藏所有操作按钮
  :deep(.ant-btn:not(.view-btn):not(.preview-btn)) {
    display: none !important;
  }

  // 隐藏上传组件
  :deep(.ant-upload) {
    display: none !important;
  }

  // 隐藏删除图标
  :deep(.ant-tag-close-icon) {
    display: none !important;
  }

  // 隐藏添加按钮
  :deep(.add-btn) {
    display: none !important;
  }

  // 隐藏删除按钮
  :deep(.delete-btn) {
    display: none !important;
  }

  // 保留查看相关的按钮
  :deep(.ant-btn) {
    &.view-btn,
    &.preview-btn,
    &[class*="view"],
    &[class*="preview"] {
      display: inline-block !important;
      pointer-events: auto !important;
    }
  }

  // 保留文件标签的点击查看功能
  :deep(.ant-tag) {
    cursor: pointer !important;
    pointer-events: auto !important;

    &:hover {
      opacity: 0.8;
    }
  }

  // 表格行只读样式
  :deep(.ant-table-tbody) {
    .ant-table-row {
      background-color: #fafafa;

      &:hover {
        background-color: #fafafa !important;
      }
    }
  }
}
</style>
